from sqlalchemy import <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base, CommonColumns, EnumType, SoftDeleteColumns
from .components import Component  # Import Component for foreign keys
# from .documents import ImportedDataRevision
# from .electrical import ElectricalNode
from .enums import (
    ControlCircuitType,
    HeatingMethodType,
    HTCircuitApplicationType,
    SensorType,
)
# from .project import Project
# from .switchboard import Feeder, Switchboard


class Pipe(CommonColumns, SoftDeleteColumns, Base):
    __tablename__ = "Pipe"

    project_id: Mapped[int] = mapped_column(ForeignKey("Project.id"), nullable=False)
    pipe_material_id: Mapped[int] = mapped_column(
        ForeignKey("Component.id"), nullable=False
    )
    nominal_diameter_mm: Mapped[float | None] = mapped_column(nullable=True)
    wall_thickness_mm: Mapped[float | None] = mapped_column(nullable=True)
    outer_diameter_mm: Mapped[float | None] = mapped_column(nullable=True)
    length_m: Mapped[float] = mapped_column(nullable=False)
    insulation_material_id: Mapped[int] = mapped_column(
        ForeignKey("Component.id"), nullable=False
    )
    insulation_thickness_mm: Mapped[float] = mapped_column(nullable=False)
    fluid_type: Mapped[str | None] = mapped_column(nullable=True)
    specific_heat_capacity_jkgc: Mapped[float | None] = mapped_column(nullable=True)
    viscosity_cp: Mapped[float | None] = mapped_column(nullable=True)
    freezing_point_c: Mapped[float | None] = mapped_column(nullable=True)
    calculated_heat_loss_wm: Mapped[float | None] = mapped_column(nullable=True)
    required_heat_output_wm: Mapped[float | None] = mapped_column(nullable=True)
    safety_margin_percent: Mapped[float] = mapped_column(default=0.0)

    pid: Mapped[str | None] = mapped_column(nullable=True)
    line_tag: Mapped[str | None] = mapped_column(nullable=True)
    from_location: Mapped[str | None] = mapped_column(nullable=True)
    to_location: Mapped[str | None] = mapped_column(nullable=True)
    valve_count: Mapped[int | None] = mapped_column(nullable=True, default=0)
    support_count: Mapped[int | None] = mapped_column(nullable=True, default=0)

    imported_data_revision_id: Mapped[int | None] = mapped_column(
        ForeignKey("ImportedDataRevision.id"), nullable=True
    )

    # Relationships
    project: Mapped["Project"] = relationship(back_populates="pipes")
    pipe_material: Mapped["Component"] = relationship(foreign_keys=[pipe_material_id])
    insulation_material: Mapped["Component"] = relationship(
        foreign_keys=[insulation_material_id]
    )
    htcircuit: Mapped["HTCircuit | None"] = relationship(
        back_populates="pipe", uselist=False, cascade="all, delete-orphan"
    )
    imported_revision: Mapped["ImportedDataRevision | None"] = relationship(
        back_populates="pipes"
    )
    electrical_nodes: Mapped[list["ElectricalNode"]] = relationship(
        back_populates="related_pipe", cascade="all, delete-orphan"
    )

    __table_args__ = (
        UniqueConstraint("project_id", "name", name="uq_pipe_project_name"),
    )

    def __repr__(self):
        return f"<Pipe(id={self.id}, name='{self.name}', project_id={self.project_id})>"


class Vessel(CommonColumns, SoftDeleteColumns, Base):
    __tablename__ = "Vessel"

    project_id: Mapped[int] = mapped_column(ForeignKey("Project.id"), nullable=False)
    material_id: Mapped[int] = mapped_column(ForeignKey("Component.id"), nullable=False)
    dimensions_json: Mapped[str] = mapped_column(nullable=False)
    surface_area_m2: Mapped[float] = mapped_column(nullable=False)
    insulation_material_id: Mapped[int] = mapped_column(
        ForeignKey("Component.id"), nullable=False
    )
    insulation_thickness_mm: Mapped[float] = mapped_column(nullable=False)
    fluid_type: Mapped[str | None] = mapped_column(nullable=True)
    specific_heat_capacity_jkgc: Mapped[float | None] = mapped_column(nullable=True)
    viscosity_cp: Mapped[float | None] = mapped_column(nullable=True)
    freezing_point_c: Mapped[float | None] = mapped_column(nullable=True)
    calculated_heat_loss_w: Mapped[float | None] = mapped_column(nullable=True)
    required_heat_output_w: Mapped[float | None] = mapped_column(nullable=True)
    safety_margin_percent: Mapped[float] = mapped_column(default=0.0)

    pid: Mapped[str | None] = mapped_column(nullable=True)
    equipment_tag: Mapped[str | None] = mapped_column(nullable=True)

    imported_data_revision_id: Mapped[int | None] = mapped_column(
        ForeignKey("ImportedDataRevision.id"), nullable=True
    )

    # Relationships
    project: Mapped["Project"] = relationship(back_populates="vessels")
    material: Mapped["Component"] = relationship(foreign_keys=[material_id])
    insulation_material: Mapped["Component"] = relationship(
        foreign_keys=[insulation_material_id]
    )
    htcircuit: Mapped["HTCircuit | None"] = relationship(
        back_populates="vessel", uselist=False, cascade="all, delete-orphan"
    )
    imported_revision: Mapped["ImportedDataRevision | None"] = relationship(
        back_populates="vessels"
    )
    electrical_nodes: Mapped[list["ElectricalNode"]] = relationship(
        back_populates="related_vessel", cascade="all, delete-orphan"
    )

    __table_args__ = (
        UniqueConstraint("project_id", "name", name="uq_vessel_project_name"),
    )

    def __repr__(self):
        return (
            f"<Vessel(id={self.id}, name='{self.name}', project_id={self.project_id})>"
        )


class ControlCircuit(CommonColumns, SoftDeleteColumns, Base):
    __tablename__ = "ControlCircuit"

    switchboard_id: Mapped[int | None] = mapped_column(
        ForeignKey("Switchboard.id"), nullable=True
    )
    type: Mapped[ControlCircuitType] = mapped_column(
        EnumType(ControlCircuitType), nullable=False
    )
    sensor_type: Mapped[SensorType] = mapped_column(
        EnumType(SensorType), nullable=False
    )

    primary_setpoint_c: Mapped[float] = mapped_column(nullable=False)
    limiting_setpoint_c: Mapped[float | None] = mapped_column(nullable=True)
    has_limiting_function: Mapped[bool] = mapped_column(
        Boolean, default=False, nullable=False
    )

    control_philosophy: Mapped[str | None] = mapped_column(nullable=True)

    # Relationships
    switchboard: Mapped["Switchboard | None"] = relationship()
    htcircuits: Mapped[list["HTCircuit"]] = relationship(
        back_populates="control_circuit"
    )
    control_circuit_components: Mapped[list["ControlCircuitComponent"]] = relationship(
        back_populates="control_circuit", cascade="all, delete-orphan"
    )
    electrical_nodes: Mapped[list["ElectricalNode"]] = relationship(
        back_populates="related_control_circuit", cascade="all, delete-orphan"
    )

    __table_args__ = (
        UniqueConstraint("name", "switchboard_id", name="uq_control_circuit_name_swbd"),
    )

    def __repr__(self):
        return (
            f"<ControlCircuit(id={self.id}, name='{self.name}', type='{self.type.value}', "
            f"primary_setpoint={self.primary_setpoint_c}°C, has_limiting={self.has_limiting_function})>"
        )


class HTCircuit(CommonColumns, SoftDeleteColumns, Base):
    __tablename__ = "HTCircuit"

    feeder_id: Mapped[int] = mapped_column(ForeignKey("Feeder.id"), nullable=False)
    heat_tracing_cable_id: Mapped[int] = mapped_column(
        ForeignKey("Component.id"), nullable=False
    )
    pipe_id: Mapped[int | None] = mapped_column(ForeignKey("Pipe.id"), nullable=True)
    vessel_id: Mapped[int | None] = mapped_column(
        ForeignKey("Vessel.id"), nullable=True
    )

    control_circuit_id: Mapped[int | None] = mapped_column(
        ForeignKey("ControlCircuit.id"), nullable=True
    )

    calculated_load_amps: Mapped[float | None] = mapped_column(nullable=True)
    calculated_load_kw: Mapped[float | None] = mapped_column(nullable=True)
    required_length_m: Mapped[float | None] = mapped_column(nullable=True)
    number_of_circuits: Mapped[int] = mapped_column(default=1)

    isometric_no: Mapped[str | None] = mapped_column(nullable=True)
    schedule_no: Mapped[str | None] = mapped_column(nullable=True)
    schedule_page: Mapped[str | None] = mapped_column(nullable=True)
    schedule_revision: Mapped[str | None] = mapped_column(nullable=True)

    application_type: Mapped[HTCircuitApplicationType | None] = mapped_column(
        EnumType(HTCircuitApplicationType), nullable=True
    )
    heating_method: Mapped[HeatingMethodType | None] = mapped_column(
        EnumType(HeatingMethodType), nullable=True
    )

    # Relationships
    feeder: Mapped["Feeder"] = relationship(back_populates="htcircuits")
    heat_tracing_cable: Mapped["Component"] = relationship(
        foreign_keys=[heat_tracing_cable_id]
    )
    pipe: Mapped["Pipe | None"] = relationship(back_populates="htcircuit")
    vessel: Mapped["Vessel | None"] = relationship(back_populates="htcircuit")
    control_circuit: Mapped["ControlCircuit | None"] = relationship(
        back_populates="htcircuits"
    )
    htcircuit_components: Mapped[list["HTCircuitComponent"]] = relationship(
        back_populates="htcircuit", cascade="all, delete-orphan"
    )

    __table_args__ = (
        UniqueConstraint("feeder_id", "name", name="uq_htcircuit_feeder_name"),
    )

    def __repr__(self):
        return (
            f"<HTCircuit(id={self.id}, name='{self.name}', feeder_id={self.feeder_id})>"
        )


# Junction tables for components specific to heat tracing and control circuits
class ControlCircuitComponent(Base):
    __tablename__ = "ControlCircuitComponent"
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)

    control_circuit_id: Mapped[int] = mapped_column(
        ForeignKey("ControlCircuit.id"), nullable=False
    )
    component_id: Mapped[int] = mapped_column(
        ForeignKey("Component.id"), nullable=False
    )
    quantity: Mapped[int] = mapped_column(default=1)
    position: Mapped[str | None] = mapped_column(nullable=True)

    control_circuit: Mapped["ControlCircuit"] = relationship(
        back_populates="control_circuit_components"
    )
    component: Mapped["Component"] = relationship()

    __table_args__ = (
        UniqueConstraint(
            "control_circuit_id", "component_id", "position", name="uq_control_comp"
        ),
    )

    def __repr__(self):
        return f"<ControlCircuitComponent(ctrl_id={self.control_circuit_id}, comp_id={self.component_id}, qty={self.quantity})>"


class HTCircuitComponent(Base):
    __tablename__ = "HTCircuitComponent"
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)

    htcircuit_id: Mapped[int] = mapped_column(
        ForeignKey("HTCircuit.id"), nullable=False
    )
    component_id: Mapped[int] = mapped_column(
        ForeignKey("Component.id"), nullable=False
    )
    quantity: Mapped[int] = mapped_column(default=1)
    position: Mapped[str | None] = mapped_column(nullable=True)

    htcircuit: Mapped["HTCircuit"] = relationship(back_populates="htcircuit_components")
    component: Mapped["Component"] = relationship()

    __table_args__ = (
        UniqueConstraint(
            "htcircuit_id", "component_id", "position", name="uq_htc_comp"
        ),
    )

    def __repr__(self):
        return f"<HTCircuitComponent(htc_id={self.htcircuit_id}, comp_id={self.component_id}, qty={self.quantity})>"
