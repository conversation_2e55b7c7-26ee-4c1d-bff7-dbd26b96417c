# Next AI Agent Implementation Prompt

## 🎯 **Mission: Implement Document Entity**

You are continuing the implementation of the Ultimate Electrical Designer backend. The core business entities and supporting infrastructure are complete, and you're implementing the Document Entity that provides document management and report generation functionality.

## 📋 **Current State Summary**

### ✅ **Completed Foundation (100%)**
1. **Project Entity** - Complete with all 5 layers + comprehensive tests
2. **Component Entity** - Complete with catalog management and validation
3. **Heat Tracing Entity** - Complete with design workflow and calculations integration
4. **Electrical Entity** - Complete with cable sizing, voltage drop calculations, and standards integration
5. **Switchboard Entity** - Complete with electrical distribution and load management
6. **User Entity** - Complete with authentication, authorization, and user management
7. **Calculations Layer** - Engineering calculations (heat loss, electrical sizing, voltage drop)
8. **Standards Layer** - TR 50410, IEC 60079-30-1 compliance validation
9. **Model-Level Validation** - SQLAlchemy event listeners for data integrity

### 🔧 **Established Architecture Patterns**
- **5-Layer Architecture**: Model → Repository → Service → API → Tests
- **Comprehensive Error Handling**: Custom exceptions with detailed error context
- **Standards Integration**: Calculations integrate with compliance validation
- **Robust Testing**: Unit tests for each layer with mocking strategies
- **Type Safety**: Full type hints and Pydantic validation throughout

## 🎯 **Your Task: Document Entity Implementation**

### **Priority**: HIGH - Document Management and Report Generation
The Document Entity provides critical document management functionality including data import/export, report generation, and calculation standards management.

### **Entity to Implement**:

#### **Document Entity** (Already exists in `backend/core/models/documents.py`):
- **ImportedDataRevision** - Manages imported data files with revision tracking
- **ExportedDocument** - Manages generated reports and exported documents
- **CalculationStandard** - Manages engineering calculation standards and parameters

### **Key Business Requirements**:
- **Data Import Management**: Track imported data files with revision control
- **Document Generation**: Generate engineering reports and export documents
- **Standards Management**: Manage calculation standards and compliance parameters
- **Version Control**: Track document revisions and maintain active versions
- **User Integration**: Track which users imported/generated documents

### **Required Implementation (Follow Established Patterns)**

#### 1. **Document Schemas** (`backend/core/schemas/document_schemas.py`)
**Create comprehensive Pydantic schemas for all document entities:**

- **ImportedDataRevisionCreateSchema/UpdateSchema/ReadSchema** - Data import management with revision tracking
- **ExportedDocumentCreateSchema/UpdateSchema/ReadSchema** - Document generation and export management
- **CalculationStandardCreateSchema/UpdateSchema/ReadSchema** - Engineering standards management
- **Document Summary Schemas** - Lightweight schemas for listings and summaries
- **File Management Schemas** - File upload, validation, and processing schemas
- **Validation Rules** - File format validation, revision control, and business constraints

**Key Requirements:**
- Integrate with user entity for tracking import/export operations
- Validate file formats and data integrity for imports
- Support revision control with active version management
- Include project-scoped document management
- Validate calculation standards against engineering requirements

#### 2. **Document Repositories** (`backend/core/repositories/document_repository.py`)
**Extend BaseRepository for each document model:**

- **ImportedDataRevisionRepository** - CRUD + project-scoped queries + revision management
- **ExportedDocumentRepository** - CRUD + document generation + version control
- **CalculationStandardRepository** - CRUD + standards management + parameter queries
- **Complex Queries** - Active revision queries, document history, standards lookup
- **Performance Optimization** - Eager loading for related projects and users

**Key Requirements:**
- Project-scoped queries for data isolation
- Revision control with active version management
- File path and URL management for document storage
- Standards parameter JSON validation and queries
- User tracking for audit trails

#### 3. **Document Service** (`backend/core/services/document_service.py`)
**Business logic layer for document management:**

- **Data Import Management** - File upload, validation, and processing workflows
- **Document Generation** - Report generation and export functionality
- **Standards Management** - Calculation standards and parameter management
- **Revision Control** - Version management and active revision tracking
- **File Management** - File storage, retrieval, and cleanup operations
- **Integration** - Connection to project, user, and calculation entities

**Key Requirements:**
- File upload validation and processing
- Document generation from project data
- Standards parameter validation and management
- Revision control with conflict resolution
- Integration with calculations and standards layers

#### 4. **Document API Routes** (`backend/api/v1/document_routes.py`)
**RESTful endpoints for document operations:**

- **Import Endpoints** - File upload, validation, and import processing
- **Export Endpoints** - Document generation and download functionality
- **Standards Endpoints** - Calculation standards management
- **Revision Endpoints** - Version control and history management
- **File Management Endpoints** - File storage and retrieval operations

**Key Requirements:**
- File upload handling with validation
- Document generation and streaming responses
- Standards CRUD operations with parameter validation
- Revision control with conflict detection
- Project-scoped access control

**Critical Integration Points:**
```python
# Example integration patterns
from backend.core.services.project_service import ProjectService
from backend.core.services.user_service import UserService
from backend.core.services.calculation_service import CalculationService

# Document service integration with project data
document_service.generate_heat_tracing_report(project_id)
document_service.import_component_data(file_path, project_id, user_id)

# Standards service integration with calculations
document_service.update_calculation_standard(standard_id, parameters)
calculation_service.apply_standard(standard_id, calculation_type)
```

#### 5. **Comprehensive Test Suite**
**Follow established testing patterns:**

- **Document Schema Tests** - File validation, revision control, standards management
- **Repository Tests** - CRUD operations, complex queries, file management
- **Service Tests** - Business logic, file processing, document generation workflows
- **API Tests** - All endpoints, file upload/download, error scenarios
- **Integration Tests** - End-to-end document management and report generation workflows

## 🔗 **Critical Integration Requirements**

### **Project Entity Integration**
- Connect documents to projects for proper scoping and access control
- Generate reports from project data (heat tracing, electrical, switchboard)
- Import data files and associate with specific projects
- Validate user permissions for project document operations

### **User Entity Integration**
- Track which users imported/generated documents for audit trails
- Implement user-based access control for document operations
- Store user preferences for document generation settings
- Validate user permissions for document access

### **Calculation Integration**
- Use calculation standards for engineering report generation
- Apply standards parameters to calculation workflows
- Validate calculation standards against engineering requirements
- Support multiple calculation standard versions

### **File Management Integration**
- Implement secure file storage and retrieval
- Support multiple file formats (Excel, CSV, PDF, etc.)
- Handle file validation and processing workflows
- Manage file cleanup and storage optimization

## 📚 **Reference Implementation Patterns**

### **Study These Completed Examples:**
1. **Project Entity** - `backend/core/schemas/project_schemas.py` for schema patterns
2. **User Entity** - `backend/core/services/user_service.py` for authentication patterns
3. **Switchboard Entity** - `backend/core/schemas/switchboard_schemas.py` for complex validation patterns
4. **Heat Tracing Entity** - `backend/core/services/heat_tracing_service.py` for calculations integration
5. **Component Repository** - `backend/core/repositories/component_repository.py` for advanced queries
6. **Project API** - `backend/api/v1/project_routes.py` for comprehensive endpoint patterns

### **Follow These Conventions:**
- Use `BaseSoftDeleteSchema` for read schemas with soft delete fields
- Implement comprehensive logging with contextual information
- Use custom exceptions from `backend.core.errors.exceptions`
- Follow established error handling and transaction management patterns
- Maintain high test coverage (>90%) with proper mocking strategies
- Use proper file handling and validation patterns for document management

## 🎯 **Success Criteria**

### **Functional Requirements:**
- [ ] Complete document management workflow with file handling
- [ ] Data import and export functionality with validation
- [ ] Report generation from project data (heat tracing, electrical, switchboard)
- [ ] Calculation standards management with parameter validation
- [ ] Revision control with active version management
- [ ] File storage and retrieval with security controls

### **Quality Requirements:**
- [ ] >90% test coverage across all layers
- [ ] Comprehensive type hints and documentation
- [ ] Proper error handling with detailed error messages
- [ ] File handling best practices with validation and security
- [ ] Integration tests demonstrating end-to-end workflows

### **Integration Requirements:**
- [ ] Seamless integration with project entity for document scoping
- [ ] User tracking for audit trails and access control
- [ ] Calculation standards integration with engineering workflows
- [ ] File management with proper storage and cleanup
- [ ] Report generation from all entity data sources

## 🚀 **Getting Started**

1. **Review the existing models** in `backend/core/models/documents.py`
2. **Study the user integration** - Examine the working user service and authentication patterns
3. **Examine the project patterns** - Research project-scoped operations and access control
4. **Follow the established patterns** from Project, User, Switchboard, and Heat Tracing entities
5. **Start with schemas** - Build the foundation with comprehensive validation and file handling
6. **Integrate early and often** - Test file handling and document generation continuously

## 📖 **Key Documentation References**

- `backend/docs/switchboard-entity-completion-summary.md` - Recent entity implementation example
- `backend/docs/user-entity-completion-summary.md` - User integration patterns
- `backend/docs/core/models/validation-architecture.md` - Model validation patterns
- `backend/docs/implementation-progress.md` - Current progress and patterns
- `backend/docs/project-entity-completion-summary.md` - Established conventions
- `backend/docs/heat-tracing-implementation-summary.md` - Complex entity implementation example

## 📁 **File Management Considerations**

### **File Handling & Security:**
- Validate file types and sizes before processing
- Implement secure file storage with proper access controls
- Use virus scanning for uploaded files
- Sanitize file names and paths
- Implement file cleanup and storage management

### **Data Processing:**
- Validate imported data against schema requirements
- Handle large file processing with streaming
- Implement proper error handling for file operations
- Support multiple file formats (Excel, CSV, PDF)
- Maintain data integrity during import/export operations

**Remember**: You're building on a solid, tested foundation with 6 complete entities as examples. The user and project entities provide excellent integration patterns for document management. Focus on creating robust file handling and document generation capabilities that integrate seamlessly with the existing engineering design workflows! 📄🔧
