{"tests/test_schemas/test_project_schemas.py::TestProjectReadSchema::test_from_orm_conversion": true, "tests/test_schemas/test_project_schemas.py::TestProjectReadSchema::test_read_schema_includes_all_fields": true, "tests/test_schemas/test_project_schemas.py::TestProjectSummarySchema::test_summary_includes_essential_fields": true, "tests/test_schemas/test_project_schemas.py::TestProjectSummarySchema::test_summary_excludes_detailed_fields": true, "tests/test_schemas/test_project_schemas.py::TestProjectListResponseSchema::test_list_response_structure": true, "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_create_project": true, "tests/test_api/test_heat_tracing_routes.py": true, "tests/test_api/test_project_routes.py": true, "tests/test_repositories/test_project_repository.py": true, "tests/test_schemas/test_component_schemas.py": true, "tests/test_schemas/test_project_schemas.py": true, "tests/test_services/test_component_service.py": true, "tests/test_services/test_project_service.py": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceVesselOperations": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceCalculations": true, "tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceDesignWorkflow": true}